<template>
	<div
		class="service-card"
		:style="{
			gridColumn: cols === 2 ? 'span 2' : 'span 1',
			background: gradientWithAlpha,
		}"
		:data-cols="cols"
	>
		<div
			class="card-content"
			:class="{
				'cols-2': cols === 2,
				'cols-1': cols === 1,
			}"
		>
			<component
				:is="TablerIcon"
				v-if="cols === 1 && icon"
				class="icon left"
				:size="75"
				:color="color"
				stroke-width="2"
			/>
			<div class="text">
				<div class="title">{{ title }}</div>
				<div class="desc">{{ desc }}</div>
			</div>
			<component
				:is="TablerIcon"
				v-if="cols === 2 && icon"
				class="icon right"
				:size="150"
				:color="color"
				stroke-width="2"
			/>
		</div>
	</div>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";
import { computed } from "vue";

function addAlphaToGradient(gradient, alpha = 0.2) {
	if (!gradient) return "";
	return gradient
		.replace(/#([0-9a-fA-F]{6})/g, (match, hex) => {
			const r = parseInt(hex.substring(0, 2), 16);
			const g = parseInt(hex.substring(2, 4), 16);
			const b = parseInt(hex.substring(4, 6), 16);
			return `rgba(${r},${g},${b},${alpha})`;
		})
		.replace(
			/rgb\((\d+), ?(\d+), ?(\d+)\)/g,
			(match, r, g, b) => `rgba(${r},${g},${b},${alpha})`
		)
		.replace(
			/rgba\((\d+), ?(\d+), ?(\d+), ?[0-9.]+\)/g,
			(match, r, g, b) => `rgba(${r},${g},${b},${alpha})`
		);
}

const props = defineProps({
	icon: String,
	title: String,
	desc: String,
	gradient: String,
	color: {
		type: String,
		default: "#fff",
	},
	cols: {
		type: Number,
		default: 1,
	},
});

const TablerIcon = computed(() => {
	return props.icon && TablerIcons[props.icon]
		? TablerIcons[props.icon]
		: TablerIcons.IconInfoCircle;
});

const gradientWithAlpha = computed(() => addAlphaToGradient(props.gradient));
</script>

<style scoped>
.service-card {
	border-radius: 20px;
	padding: 20px;
	color: #fff;
	display: flex;
	flex-direction: column;
	transition: transform 0.13s;
	position: relative;
	backdrop-filter: blur(10px);
	will-change: backdrop-filter;
	min-width: 0;
}

.service-card:hover {
	transform: scale(1.03);
}

.card-content {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	justify-content: space-between;
}

.service-card:has(.card-content.cols-1) {
	aspect-ratio: 1/1;
}

.card-content.cols-2 {
	flex-direction: row;
}

.card-content.cols-1 .icon.left {
	margin-right: 18px;
	order: 0;
}
.card-content.cols-2 .icon.right {
	margin-left: 18px;
	order: 2;
	margin: auto;
}
.card-content.cols-2 .text {
	order: 1;
}
.card-content .text {
	min-width: 0;
}

.title {
	font-size: 2rem;
	margin-top: 10px;
}

.desc {
	font-size: 1.05rem;
}
.icon {
	flex: none;
	display: flex;
	align-items: center;
	justify-content: center;
	aspect-ratio: 1;
}

@media screen and (max-width: 768px) {
	.service-card:has(.card-content.cols-1) {
		aspect-ratio: initial;
	}

	.card-content.cols-2 {
		flex-direction: column-reverse;
	}

	.card-content.cols-2 .icon.right {
		width: 75px;
		height: 75px;
		margin: 0;
	}
}
</style>
