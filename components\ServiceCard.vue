<template>
	<div
		class="service-card"
		:style="{
			gridColumn: cols === 2 ? 'span 2' : 'span 1',
			background: gradientWithAlpha,
		}"
		:data-cols="cols"
	>
		<div
			class="card-content"
			:class="{
				'cols-2': cols === 2,
				'cols-1': cols === 1,
			}"
		>
			<component
				:is="TablerIcon"
				v-if="cols === 1 && icon"
				class="icon left"
				:size="75"
				:color="color"
				stroke-width="2"
			/>
			<div class="text">
				<div class="title">{{ title }}</div>
				<div class="desc">{{ desc }}</div>
			</div>
			<component
				:is="TablerIcon"
				v-if="cols === 2 && icon"
				class="icon right"
				:size="150"
				:color="color"
				stroke-width="2"
			/>
		</div>
	</div>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";
import { computed } from "vue";

function addAlphaToGradient(gradient, alpha = 0.2) {
	if (!gradient) return "";
	return gradient
		.replace(/#([0-9a-fA-F]{6})/g, (match, hex) => {
			const r = parseInt(hex.substring(0, 2), 16);
			const g = parseInt(hex.substring(2, 4), 16);
			const b = parseInt(hex.substring(4, 6), 16);
			return `rgba(${r},${g},${b},${alpha})`;
		})
		.replace(
			/rgb\((\d+), ?(\d+), ?(\d+)\)/g,
			(match, r, g, b) => `rgba(${r},${g},${b},${alpha})`
		)
		.replace(
			/rgba\((\d+), ?(\d+), ?(\d+), ?[0-9.]+\)/g,
			(match, r, g, b) => `rgba(${r},${g},${b},${alpha})`
		);
}

const props = defineProps({
	icon: String,
	title: String,
	desc: String,
	gradient: String,
	color: {
		type: String,
		default: "#fff",
	},
	cols: {
		type: Number,
		default: 1,
	},
});

const TablerIcon = computed(() => {
	return props.icon && TablerIcons[props.icon]
		? TablerIcons[props.icon]
		: TablerIcons.IconInfoCircle;
});

const gradientWithAlpha = computed(() => addAlphaToGradient(props.gradient));
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_service-card.scss */
</style>
