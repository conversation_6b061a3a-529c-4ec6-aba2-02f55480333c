<template>
	<section>
		<Transition name="fade-slide" appear>
			<div class="content">
				<div class="status green">Низкая загруженность проектами</div>
				<span class="title">
					Студия которой можно доверить свою
					<span class="serif">цифровизацию</span>
				</span>
				<div class="btns">
					<NuxtLink to="/contacts" class="btn accent"
						><IconMessageCircleFilled />Обсудить проект</NuxtLink
					>
					<NuxtLink to="/cases" class="btn"><IconEyeFilled />Посмотреть примеры</NuxtLink>
				</div>
			</div>
		</Transition>
	</section>
</template>

<script setup>
import { NuxtLink } from "#components";
import { IconEyeFilled, IconMessageCircleFilled } from "@tabler/icons-vue";
</script>

<style scoped>
section, .content {
	align-items: center;
	text-align: center;
	justify-content: center;
}

.btns {
	display: flex;
	gap: 15px;
	justify-content: center;
	flex-wrap: wrap;
}

.title {
	font-size: 3.5em;
	font-size: clamp(2.5rem, 4vw, 3.5em);
	line-height: 90%;
	max-width: 1100px;
}

.serif {
	font-family: "Caveat", serif;
	color: #569f7a;
	font-size: 96px;
	font-size: clamp(4rem, 4vw, 5rem);
}

.status {
	padding: 12px;
	border-radius: 50px;
	background-color: rgb(255, 255, 255, 0.2);
}

.status:before {
	content: "• ";
	font-size: 2em;
	line-height: 0;
	color: #569f7a;
	vertical-align: middle;
}

.green {
	background-color: rgb(0, 163, 97, 0.2);
}
span {
	text-wrap: balance;
}

.fade-slide-enter-active {
	transition: all 0.8s ease;
}
.fade-slide-enter-from {
	opacity: 0;
	transform: translateY(20px);
	filter: blur(5px);
}
</style>
