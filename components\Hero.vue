<template>
	<section class="hero">
		<Transition name="fade-slide" appear>
			<div class="content">
				<div class="status green">Низкая загруженность проектами</div>
				<span class="title">
					Студия которой можно доверить свою
					<span class="serif">цифровизацию</span>
				</span>
				<div class="btns">
					<NuxtLink to="/contacts" class="btn accent"
						><IconMessageCircleFilled />Обсудить проект</NuxtLink
					>
					<NuxtLink to="/cases" class="btn"
						><IconEyeFilled />Посмотреть примеры</NuxtLink
					>
				</div>
			</div>
		</Transition>
	</section>
</template>

<script setup>
import { NuxtLink } from "#components";
import { IconEyeFilled, IconMessageCircleFilled } from "@tabler/icons-vue";
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_hero.scss */
</style>
