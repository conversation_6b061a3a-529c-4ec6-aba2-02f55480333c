// public/workers/perlin.worker.js

// Импортируем наш класс <PERSON> (путь может отличаться)
self.importScripts("/utils/perlin.js");

let canvas,
	ctx,
	width,
	height,
	time = 0;
const perlin = new self.Perlin();

// --- Параметры и константы остаются здесь ---
const NOISE_PARAMETERS = {
	scale: 0.003,
	threshold: 0.1,
	contourWidth: 0.07,
	animationSpeed: 0.005,
	waveFrequency: 0.005,
	waveAmplitude: 0.45,
};

const COLORS = {
	WHITE: [255, 255, 255, 255],
	TRANSPARENT_BLACK: [0, 0, 0, 0],
};
const PIXEL_CHANNEL_COUNT = 4;
let imageData = null;

function drawPerlinContours() {
	if (!ctx || width <= 0 || height <= 0) return;

	if (!imageData || imageData.width !== width || imageData.height !== height) {
		imageData = ctx.createImageData(width, height);
	}
	const data = imageData.data;

	const { scale, threshold, contourWidth, waveFrequency, waveAmplitude } =
		NOISE_PARAMETERS;
	const { WHITE, TRANSPARENT_BLACK } = COLORS;

	for (let y = 0; y < height; y++) {
		for (let x = 0; x < width; x++) {
			const offsetX = Math.sin(y * waveFrequency + time) * waveAmplitude;
			const offsetY = Math.cos(x * waveFrequency + time) * waveAmplitude;

			const noiseValue = perlin.noise(x * scale + offsetX, y * scale + offsetY);

			const isContour = Math.abs(noiseValue - threshold) < contourWidth;
			const index = (y * width + x) * PIXEL_CHANNEL_COUNT;
			const color = isContour ? WHITE : TRANSPARENT_BLACK;

			data[index] = color[0];
			data[index + 1] = color[1];
			data[index + 2] = color[2];
			data[index + 3] = color[3];
		}
	}
	ctx.putImageData(imageData, 0, 0);
}

function animate() {
	time += NOISE_PARAMETERS.animationSpeed;
	drawPerlinContours();
	requestAnimationFrame(animate);
}

// Слушаем сообщения от основного потока
self.onmessage = (e) => {
	const { type, payload } = e.data;

	if (type === "init") {
		canvas = payload.canvas;
		ctx = canvas.getContext("2d");
		width = payload.width;
		height = payload.height;
		canvas.width = width;
		canvas.height = height;
		animate(); // Запускаем анимацию внутри Worker'а
	}

	if (type === "resize") {
		width = payload.width;
		height = payload.height;
		if (canvas) {
			canvas.width = width;
			canvas.height = height;
		}
		imageData = null; // Сбросить imageData, чтобы создать новый с правильными размерами
	}
};
