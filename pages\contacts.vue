<template>
	<section>
		<h1>Контакты</h1>
		<div class="contacts-section">
			<div class="contacts-left">
				<div class="socials">
					<a
						class="contact-card tg"
						href="https://t.me/apollon_it"
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconBrandTelegram size="72" color="#22b8e6" />
						<div>
							<div class="contact-title">Telegram</div>
							<div class="contact-sub">@apollon_it</div>
						</div>
					</a>
					<a
						class="contact-card vk"
						href="https://vk.com/apollon_it"
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconBrandVk size="72" color="#1d90ec" />
						<div>
							<div class="contact-title">VK</div>
							<div class="contact-sub">@apollon_it</div>
						</div>
					</a>
				</div>

				<div class="contact-card">
					<div class="contact-label">электронная почта</div>
					<a href="mailto:<EMAIL>" class="contact-email"
						><EMAIL></a
					>
					<div class="contact-label phone">номер телефона</div>
					<a class="contact-phone" href="tel:+79501820736"
						>+7 (950) 182-07-36</a
					>
				</div>
			</div>
			<div class="contacts-right">
				<form class="contacts-form" @submit.prevent="submitForm">
					<h2 class="form-title">Обсудим?</h2>
					<input v-model="name" required placeholder="Ваше имя *" />
					<input v-model="phone" required placeholder="Номер телефона *" />
					<textarea
						v-model="project"
						placeholder="Опишите ваш проект"
					></textarea>
					<div class="form-policy">
						Нажимая на кнопку «Отправить заявку», Вы соглашаетесь с политикой
						обработки персональных данных и получением информационных рассылок.
					</div>
					<button type="submit" class="contacts-submit">
						<IconSend />
						Отправить заявку
					</button>
					<div v-if="sent" class="form-success">Спасибо за заявку!</div>
				</form>
			</div>
		</div>
	</section>
</template>

<script setup>
import { IconBrandTelegram, IconBrandVk, IconSend } from "@tabler/icons-vue";
definePageMeta({
	title: "Контакты", // Это будет titleChunk
});
</script>
<style scoped>
/* Стили теперь в assets/scss/components/_contacts.scss */
</style>
