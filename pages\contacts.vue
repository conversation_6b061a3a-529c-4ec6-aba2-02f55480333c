<template>
	<section>
		<h1>Контакты</h1>
		<div class="contacts-section">
			<div class="contacts-left">
				<div class="socials">
					<a
						class="contact-card tg"
						href="https://t.me/apollon_it"
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconBrandTelegram size="72" color="#22b8e6" />
						<div>
							<div class="contact-title">Telegram</div>
							<div class="contact-sub">@apollon_it</div>
						</div>
					</a>
					<a
						class="contact-card vk"
						href="https://vk.com/apollon_it"
						target="_blank"
						rel="noopener noreferrer"
					>
						<IconBrandVk size="72" color="#1d90ec" />
						<div>
							<div class="contact-title">VK</div>
							<div class="contact-sub">@apollon_it</div>
						</div>
					</a>
				</div>

				<div class="contact-card">
					<div class="contact-label">электронная почта</div>
					<a href="mailto:<EMAIL>" class="contact-email"
						><EMAIL></a
					>
					<div class="contact-label phone">номер телефона</div>
					<a class="contact-phone" href="tel:+79501820736"
						>+7 (950) 182-07-36</a
					>
				</div>
			</div>
			<div class="contacts-right">
				<form class="contacts-form" @submit.prevent="submitForm">
					<h2 class="form-title">Обсудим?</h2>
					<input v-model="name" required placeholder="Ваше имя *" />
					<input v-model="phone" required placeholder="Номер телефона *" />
					<textarea
						v-model="project"
						placeholder="Опишите ваш проект"
					></textarea>
					<div class="form-policy">
						Нажимая на кнопку «Отправить заявку», Вы соглашаетесь с политикой
						обработки персональных данных и получением информационных рассылок.
					</div>
					<button type="submit" class="contacts-submit">
						<IconSend />
						Отправить заявку
					</button>
					<div v-if="sent" class="form-success">Спасибо за заявку!</div>
				</form>
			</div>
		</div>
	</section>
</template>

<script setup>
import { IconBrandTelegram, IconBrandVk, IconSend } from "@tabler/icons-vue";
definePageMeta({
	title: "Контакты", // Это будет titleChunk
});
</script>
<style scoped>
.contacts-section {
	display: flex;
	gap: 32px;
}
.contacts-left,
.contacts-right {
	flex: 1 1 340px;
	display: flex;
	flex-direction: column;
	gap: 24px;
}
.contact-card {
	background: rgba(82, 82, 82, 0.2);
	border-radius: 20px;
	padding: 28px 24px;
	color: #fff;
	width: 100%;
	font-size: 1.1rem;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	backdrop-filter: blur(9px);
}

.contact-card.vk {
	background: radial-gradient(
		152.53% 141.42% at 0% 100%,
		#0077ff33 0%,
		#52525233 100%
	);
}

.contact-card.vk,
.contact-card.tg {
	aspect-ratio: 1;
	gap: 20px;
}

.contact-card.tg {
	background: radial-gradient(
		152.53% 141.42% at 100% 100%,
		#26a5e433 0%,
		#52525233 100%
	);
}

.socials {
	display: flex;
	gap: 24px;
}
.contact-card.social {
	display: flex;
	flex-direction: column;
	gap: 16px;
}
.contact-title {
	font-weight: 700;
	font-size: 2em;
}
.contact-sub {
	color: rgba(255, 255, 255, 0.6);
	font-size: 1.2em;
}
.contact-label {
	color: #8e9296;
	margin-bottom: 2px;
	font-size: clamp(1em, 1.5vw, 1.3em);
}
.contact-label.phone {
	margin-top: 18px;
}
.contact-email,
.contact-phone {
	color: #fff;
	font-size: 2em;
	font-size: clamp(1em, 2.5vw, 2em);
}
.contacts-form {
	background: rgba(82, 82, 82, 0.2);
	border-radius: 20px;
	backdrop-filter: blur(10px);
	will-change: backdrop-filter;
	padding: 28px 24px;
	display: flex;
	flex-direction: column;
	gap: 18px;
	color: #fff;
	height: 100%;
	justify-content: center;
}
.form-title {
	font-size: 2em;
	font-weight: 700;
	margin-bottom: 8px;
}
.contacts-form input,
.contacts-form textarea {
	background: #101010;
	border: none;
	border-radius: 8px;
	color: #fff;
	padding: 13px 14px;
	font-size: 1em;
	outline: none;
	margin-bottom: 0;
}
.contacts-form textarea {
	min-height: 68px;
	resize: vertical;
}
.form-policy {
	font-size: 0.94em;
	color: #b6b6b6;
	line-height: 1.4;
}
.contacts-submit {
	background: #1cb17c;
	color: #fff;
	font-weight: 600;
	border: none;
	border-radius: 12px;
	padding: 13px;
	cursor: pointer;
	font-size: 1.12em;
	margin-top: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	transition: background 0.2s;
}
.contacts-submit:hover {
	background: #179a6a;
}
.btn-icon {
	width: 22px;
	height: 22px;
	fill: #fff;
}
.form-success {
	color: #38e586;
	margin-top: 9px;
	font-size: 1.08em;
}
@media (max-width: 900px) {
	.contacts-section {
		flex-direction: column;
		padding: 18px 6px;
	}
	.socials {
		flex-wrap: wrap;
	}
	.contact-card {
		aspect-ratio: initial !important;
	}
}
</style>
