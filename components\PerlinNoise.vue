<template>
	<canvas ref="perlinCanvas"></canvas>
</template>

<script setup>
import { ref } from "vue";
import { usePerlinAnimation } from "~/composables/usePerlinAnimation";

const perlinCanvas = ref(null);

// Просто вызываем наш хук, передавая ему ref на canvas
usePerlinAnimation(perlinCanvas);
</script>

<style scoped>
canvas {
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	/* Размеры теперь устанавливаются через JS для соответствия разрешению,
     но CSS задает поведение на странице */
	width: 100vw;
	height: 100vh;
	mix-blend-mode: overlay;
	z-index: -1;
}
</style>
