<template>
	<div class="perlin-noise">
		<canvas ref="perlinCanvas"></canvas>
	</div>
</template>

<script setup>
import { ref } from "vue";
import { usePerlinAnimation } from "~/composables/usePerlinAnimation";

const perlinCanvas = ref(null);

// Просто вызываем наш хук, передавая ему ref на canvas
usePerlinAnimation(perlinCanvas);
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_perlin-noise.scss */
</style>
