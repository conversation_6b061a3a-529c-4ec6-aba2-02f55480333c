// Медиа-запросы
@mixin mobile {
  @media screen and (max-width: $breakpoint-lg) {
    @content;
  }
}

@mixin tablet {
  @media screen and (max-width: $breakpoint-xl) {
    @content;
  }
}

@mixin desktop {
  @media screen and (max-width: $breakpoint-xxl) {
    @content;
  }
}

@mixin mobile-menu {
  @media (max-width: $breakpoint-xl) {
    @content;
  }
}

// Кнопки
@mixin button-base {
  padding: 16px 24px;
  border-radius: $border-radius-round;
  font-size: 1em;
  display: flex;
  align-items: center;
  gap: 10px;
  overflow: hidden;
  transition: filter $transition-slow;
  text-decoration: none;
  cursor: pointer;
  border: none;
  font-family: $font-family-primary;
  letter-spacing: $letter-spacing;

  &:hover {
    filter: brightness(1.25);
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $card-background;
  color: $text-secondary;
  border: solid 2px $text-secondary;
  box-shadow: $shadow-medium;
}

@mixin button-accent {
  @include button-base;
  background-color: $primary-green-alpha;
  color: $primary-green;
  box-shadow: $shadow-medium;
  border: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: skewX(-20deg);
    transition: all $transition-extra-slow ease;
    opacity: 0;
  }

  &:hover::before {
    left: 125%;
    opacity: 1;
  }
}

// Карточки
@mixin card-base {
  border-radius: $border-radius-large;
  background: $card-background;
  backdrop-filter: blur(10px);
  will-change: backdrop-filter;
  transition: transform $transition-fast;

  &:hover {
    transform: scale(1.03);
  }
}

@mixin case-card {
  border-radius: $border-radius-xl;
  overflow: hidden;
  display: flex;
  min-height: 320px;
  min-width: 0;
  position: relative;
  text-decoration: none;
  transition: box-shadow $transition-medium, transform $transition-medium;
  box-shadow: $shadow-small;
  background-color: $card-background-dark;
  color: $text-primary;

  &:hover {
    transform: translateY(-2px) scale(1.025);
    box-shadow: $shadow-large;
  }
}

// Сетки
@mixin grid-base($columns: $grid-columns-max, $gap: $grid-gap, $min-width: $grid-min-width) {
  --grid-layout-gap: #{$gap};
  --grid-column-count: #{$columns};
  --grid-item--min-width: #{$min-width};
  --gap-count: calc(var(--grid-column-count) - 1);
  --total-gap-width: calc(var(--gap-count) * var(--grid-layout-gap));
  --grid-item--max-width: calc(
    (100% - var(--total-gap-width)) / var(--grid-column-count)
  );
  
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(max(var(--grid-item--min-width), var(--grid-item--max-width)), 1fr)
  );
  grid-gap: var(--grid-layout-gap);
  width: 100%;
}

@mixin case-grid {
  --gap: #{$grid-gap-large};
  --min-width: #{$grid-min-width-case};
  --max-columns: #{$grid-columns-max};
  
  display: grid;
  grid-template-columns: repeat(var(--max-columns), 1fr);
  gap: var(--gap);
  width: 100%;

  .case-wide {
    grid-column: span 2;
  }

  @include desktop {
    --max-columns: #{$grid-columns-medium};
  }

  @include tablet {
    --max-columns: #{$grid-columns-mobile};
    --gap: #{$grid-gap-mobile};

    .case-wide {
      grid-column: span 1 !important;
    }
  }
}

// Текст
@mixin text-clamp($min, $preferred, $max) {
  font-size: clamp(#{$min}, #{$preferred}, #{$max});
}

@mixin text-balance {
  text-wrap: balance;
}

// Анимации
@mixin fade-slide-enter {
  transition: all 0.8s ease;

  &-from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(5px);
  }
}

@mixin page-transition {
  transition: all 0.6s $transition-easing-page;

  &-enter-from {
    opacity: 0;
    transform: translateX(30px);
  }

  &-leave-to {
    opacity: 0;
    transform: translateX(-30px);
  }
}

// Backdrop фильтры
@mixin backdrop-blur($blur: 20px) {
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
}

@mixin backdrop-mask-gradient {
  mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
  -webkit-mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
}

// Позиционирование
@mixin absolute-cover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

@mixin fixed-cover {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

// Flexbox утилиты
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  @include flex-column;
  align-items: center;
  justify-content: center;
}

// Скрытие скроллбара
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
