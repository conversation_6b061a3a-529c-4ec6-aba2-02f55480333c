// perlinWorker.js

// Реализация шума Перлина (полностью скопирована из вашего кода, чтобы быть автономной)
const Perlin = function () {
	this.p = new Array(512);
	this.grad2d = [
		[1, 1],
		[-1, 1],
		[1, -1],
		[-1, -1],
		[1, 0],
		[-1, 0],
		[0, 1],
		[0, -1],
	];

	this.init = function () {
		for (let i = 0; i < 256; i++) {
			this.p[i] = i;
		}
		for (let i = 0; i < 256; i++) {
			const r = Math.floor(Math.random() * (256 - i)) + i;
			const temp = this.p[i];
			this.p[i] = this.p[r];
			this.p[r] = temp;
		}
		for (let i = 0; i < 256; i++) {
			this.p[i + 256] = this.p[i];
		}
	};

	this.fade = function (t) {
		return t * t * t * (t * (t * 6 - 15) + 10);
	};

	this.lerp = function (a, b, t) {
		return a + t * (b - a);
	};

	this.grad = function (hash, x, y) {
		const h = hash & 7;
		const u = h < 4 ? x : y;
		const v = h < 4 ? y : x;
		return (h & 1 ? -u : u) + (h & 2 ? -2 * v : 2 * v);
	};

	this.noise = function (x, y) {
		const X = Math.floor(x) & 255;
		const Y = Math.floor(y) & 255;

		x -= Math.floor(x);
		y -= Math.floor(y);

		const u = this.fade(x);
		const v = this.fade(y);

		const A = this.p[X] + Y;
		const AA = this.p[A];
		const AB = this.p[A + 1];
		const B = this.p[X + 1] + Y;
		const BA = this.p[B];
		const BB = this.p[B + 1];

		return this.lerp(
			this.lerp(this.grad(AA, x, y), this.grad(BA, x - 1, y), u),
			this.lerp(this.grad(AB, x, y - 1), this.grad(BB, x - 1, y - 1), u),
			v
		);
	};

	this.init();
};

const perlin = new Perlin(); // Создаем экземпляр Perlin Noise

// Обработчик сообщений от основного потока
self.onmessage = function (e) {
	const {
		width,
		height,
		time,
		scale,
		threshold,
		contourWidth,
		waveFrequency,
		waveAmplitude,
		imageDataBuffer,
	} = e.data;

	// Восстанавливаем Uint8ClampedArray из буфера
	const data = new Uint8ClampedArray(imageDataBuffer);

	for (let y = 0; y < height; y++) {
		for (let x = 0; x < width; x++) {
			const offsetX = Math.sin(y * waveFrequency + time) * waveAmplitude;
			const offsetY = Math.cos(x * waveFrequency + time) * waveAmplitude;

			const noiseValue = perlin.noise(x * scale + offsetX, y * scale + offsetY);
			const isContour = Math.abs(noiseValue - threshold) < contourWidth;

			const index = (y * width + x) * 4;

			if (isContour) {
				data[index] = 255;
				data[index + 1] = 255;
				data[index + 2] = 255;
				data[index + 3] = 255;
			} else {
				data[index] = 0;
				data[index + 1] = 0;
				data[index + 2] = 0;
				data[index + 3] = 0;
			}
		}
	}

	// Отправляем буфер обратно в основной поток.
	// Используем `transferList` для эффективной передачи, не копируя данные.
	self.postMessage({ imageDataBuffer: data.buffer }, [data.buffer]);
};
