<template>
	<Header />
	<PerlinNoise />
	<NuxtLayout>
		<NuxtPage
			:transition="{
				name: 'page',
				mode: 'out-in',
			}"
		/>
	</NuxtLayout>
</template>

<script setup>
import { useRoute } from "vue-router";
import { useHead } from "#imports";

const route = useRoute();

watchEffect(() => {
	const pageTitle = route.meta.title || "Apollon Digital";
	useHead({
		title: `${pageTitle} - Apollon Digital`,
	});
});
</script>

<style>
/* Slide transition for routes */
.page-enter-active,
.page-leave-active {
	transition: all 0.6s cubic-bezier(0.075, 0.82, 0.165, 1);
}

.page-enter-from {
	opacity: 0;
	transform: translateX(30px);
}

.page-leave-to {
	opacity: 0;
	transform: translateX(-30px);
}

/* Optionally: Fade in layout (used for page load) */
.layout-enter-active {
	transition: all 0.8s ease;
}
.layout-enter-from {
	opacity: 0;
}
</style>
