<template>
	<div class="service-grid">
		<ServiceCard
			v-for="service in services"
			:key="service.id"
			:icon="service.icon"
			:title="service.title"
			:desc="service.desc"
			:cols="service.cols"
			:gradient="service.gradient"
			:color="service.color"
			class="service-card"
			:class="service.class"
		/>
	</div>
</template>

<script setup>
import { defineAsyncComponent } from "vue";

// Карточка услуги
const ServiceCard = defineAsyncComponent(() => import("./ServiceCard.vue"));

// Пример массива услуг, где icon — это имя Lucide Icon (PascalCase, например: 'Code', 'Palette', 'Rocket')
const services = [
	{
		id: 1,
		icon: "IconBrush",
		title: "Дизайн",
		desc: "разработка дизайна для мобильных приложений, веб сайтов и графический дизайн",
		cols: 1,
		gradient:
			"radial-gradient(152.53% 141.42% at 100% 100%, #9f5656 0%, #525252 100%)",
		color: "#9f5656", // зеленый
		class: "web",
	},
	{
		id: 2,
		icon: "IconAd2",
		title: "Маркетинг",
		desc: "индвидуальный подход к каждому кейсу",
		cols: 1,
		gradient:
			"radial-gradient(152.53% 141.42% at 100% 100%, #9f9356 0%, #525252 100%)",
		color: "#9f9356", // оранжевый
	},
	{
		id: 3,
		icon: "IconAppWindow",
		title: "Web-Разработка",
		desc: "сайты любой сложности, под ваши цели и задачи",
		cols: 2,
		gradient:
			"radial-gradient(152.53% 141.42% at 100% 100%, #569f7a 0%, #525252 100%)",
		color: "#569f7a", // красный
	},
	{
		id: 4,
		icon: "IconMessageChatbot",
		title: "Техническая поддержка",
		desc: "поддержка готовых продуктов и быстрое решение проблем",
		cols: 2,
		gradient:
			"radial-gradient(152.53% 141.42% at 100% 100%, #56879f 0%, #525252 100%)",
		color: "#56879f", // бирюзовый
	},
	{
		id: 5,
		icon: "IconAd2",
		title: "Битрикс24",
		desc: "интегрируем мощную платформу для автоматизации бизнеса.",
		cols: 1,
		gradient:
			"radial-gradient(152.53% 141.42% at 100% 100%, #62569f 0%, #525252 100%)",
		color: "#62569f", // бирюзовый
	},
	{
		id: 6,
		title: "и многое другое...",
		cols: 1,
		gradient: "#151515",
		color: "#fff", // бирюзовый
	},
];
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_service-card.scss */
</style>
