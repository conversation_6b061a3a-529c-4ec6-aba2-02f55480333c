<template>
	<a
		class="case-card"
		:style="{
			background: `url('${bg}') center/cover no-repeat`,
		}"
		:href="link"
		target="_blank"
		rel="noopener"
	>
		<div
			class="overlay"
			:style="{
				background: `linear-gradient(0deg, ${gradient} 5%, transparent 60%)`,
			}"
		>
			<div class="main">
				<div class="title">{{ title }}</div>
				<div class="desc">{{ desc }}</div>
				<div class="tags">
					<CaseTag
						v-for="tag in tags"
						:key="tag.id"
						:icon="tag.icon"
						:label="tag.label"
						:color="tag.color"
						:bg="tag.bg"
					/>
				</div>
			</div>
			<div class="next-btn">
				<IconArrowRight class="icon" :size="32" />
			</div>
		</div>
	</a>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";
import CaseTag from "./CaseTag.vue";

const props = defineProps({
	title: String,
	desc: String,
	tags: Array,
	bg: String,
	gradient: {
		type: String,
		default: "rgba(0,0,0,0.6)",
	},
	link: String,
	cols: {
		type: Number,
		default: 1,
	},
});

const IconArrowRight = TablerIcons.IconArrowRight;
</script>

<style scoped>
.case-card {
	border-radius: 32px;
	overflow: hidden;
	display: flex;
	min-height: 320px;
	min-width: 0;
	position: relative;
	text-decoration: none;
	transition: box-shadow 0.14s, transform 0.14s;
	box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.1);
	background-color: #222;
	color: #fff;
}
.case-card:hover {
	transform: translateY(-2px) scale(1.025);
	box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.16);
}

.overlay {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-end;
	width: 100%;
	min-height: 100%;
	padding: 14px;
}

.main {
	flex: 1 1 auto;
	min-width: 0;
}

.title {
	font-size: 2rem;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.14);
	line-height: 1.1;
}

.desc {
	font-size: 1.05rem;
	margin-bottom: 0.3em;
	line-height: 1.25;
	font-weight: 300;
}

.tags {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.next-btn {
	margin-left: 18px;
	flex-shrink: 0;
	margin-top: 14px;
	width: 54px;
	height: 54px;
	border-radius: 50%;
	background: linear-gradient(135deg, #2ecc71 70%, #44cabe 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background 0.14s;
}
.case-card:hover .next-btn {
	background: linear-gradient(135deg, #44cabe 60%, #2ecc71 100%);
}
.next-btn .icon {
	color: #fff;
}
</style>
