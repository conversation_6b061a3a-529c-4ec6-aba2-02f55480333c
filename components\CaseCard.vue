<template>
	<a
		class="case-card"
		:style="{
			background: `url('${bg}') center/cover no-repeat`,
		}"
		:href="link"
		target="_blank"
		rel="noopener"
	>
		<div
			class="overlay"
			:style="{
				background: `linear-gradient(0deg, ${gradient} 5%, transparent 60%)`,
			}"
		>
			<div class="main">
				<div class="title">{{ title }}</div>
				<div class="desc">{{ desc }}</div>
				<div class="tags">
					<CaseTag
						v-for="tag in tags"
						:key="tag.id"
						:icon="tag.icon"
						:label="tag.label"
						:color="tag.color"
						:bg="tag.bg"
					/>
				</div>
			</div>
			<div class="next-btn">
				<IconArrowRight class="icon" :size="32" />
			</div>
		</div>
	</a>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";
import CaseTag from "./CaseTag.vue";

const props = defineProps({
	title: String,
	desc: String,
	tags: Array,
	bg: String,
	gradient: {
		type: String,
		default: "rgba(0,0,0,0.6)",
	},
	link: String,
	cols: {
		type: Number,
		default: 1,
	},
});

const IconArrowRight = TablerIcons.IconArrowRight;
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_case-card.scss */
</style>
