<template>
	<div class="case-tag" :style="{ background: bg, color: color }">
		<component :is="TablerIcon" class="tag-icon" :size="22" />
		<span class="tag-label">{{ label }}</span>
	</div>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";

const props = defineProps({
	icon: String,
	label: String,
	color: {
		type: String,
		default: "#fff",
	},
	bg: {
		type: String,
		default: "rgba(255,255,255,0.12)",
	},
});

const TablerIcon = TablerIcons[props.icon] || TablerIcons.IconTag;
</script>

<style scoped>
.case-tag {
	display: inline-flex;
	align-items: center;
	gap: 7px;
	border-radius: 16px;
	font-size: 1.02rem;
	font-weight: 500;
	padding: 0.28em 1.15em 0.28em 0.7em;
	background: rgba(255, 255, 255, 0.08);
	color: #fff;
	user-select: none;
	transition: background 0.14s, color 0.14s;
	line-height: 1.2;
}
.tag-icon {
	flex-shrink: 0;
}
</style>
