<template>
	<div class="case-tag" :style="{ background: bg, color: color }">
		<component :is="TablerIcon" class="tag-icon" :size="22" />
		<span class="tag-label">{{ label }}</span>
	</div>
</template>

<script setup>
import * as TablerIcons from "@tabler/icons-vue";

const props = defineProps({
	icon: String,
	label: String,
	color: {
		type: String,
		default: "#fff",
	},
	bg: {
		type: String,
		default: "rgba(255,255,255,0.12)",
	},
});

const TablerIcon = TablerIcons[props.icon] || TablerIcons.IconTag;
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_case-card.scss */
</style>
