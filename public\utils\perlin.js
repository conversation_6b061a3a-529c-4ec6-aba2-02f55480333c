// utils/perlin.js

// Based on classic Perlin noise implementation
class Perlin {
	constructor() {
		this.p = new Uint8Array(512);
		this.init();
	}

	init() {
		const p = Array.from({ length: 256 }, (_, i) => i);
		// <PERSON>-<PERSON> shuffle
		for (let i = p.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[p[i], p[j]] = [p[j], p[i]];
		}
		// Duplicate the array for wrapping
		for (let i = 0; i < 256; i++) {
			this.p[i] = this.p[i + 256] = p[i];
		}
	}

	fade(t) {
		return t * t * t * (t * (t * 6 - 15) + 10);
	}

	lerp(a, b, t) {
		return a + t * (b - a);
	}

	// Classic gradient function for 2D noise
	grad(hash, x, y) {
		const gradients = [
			[1, 1],
			[-1, 1],
			[1, -1],
			[-1, -1],
			[1, 0],
			[-1, 0],
			[0, 1],
			[0, -1],
		];
		const g = gradients[hash & 7];
		return g[0] * x + g[1] * y;
	}

	noise(x, y) {
		const X = Math.floor(x) & 255;
		const Y = Math.floor(y) & 255;

		x -= Math.floor(x);
		y -= Math.floor(y);

		const u = this.fade(x);
		const v = this.fade(y);

		const p = this.p;
		const A = p[X] + Y;
		const B = p[X + 1] + Y;

		const AA = p[A];
		const AB = p[A + 1];
		const BA = p[B];
		const BB = p[B + 1];

		const lerpX0 = this.lerp(this.grad(AA, x, y), this.grad(BA, x - 1, y), u);
		const lerpX1 = this.lerp(
			this.grad(AB, x, y - 1),
			this.grad(BB, x - 1, y - 1),
			u
		);

		return this.lerp(lerpX0, lerpX1, v);
	}
}

self.Perlin = Perlin;
