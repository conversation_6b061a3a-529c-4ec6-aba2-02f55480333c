// Header стили
.header {
	@include flex-between;
	position: fixed;
	filter: $shadow-header;
	width: 100%;
	padding: $header-padding;
	left: 0;
	top: 0;
	z-index: $z-index-header;

	@include mobile-menu {
		padding: $header-padding-mobile;
	}

	&::before {
		content: "";
		@include absolute-cover;
		@include backdrop-blur;
		@include backdrop-mask-gradient;
		pointer-events: none;
	}

	&:has(.nav.open) {
		background: $overlay-background;
	}

	.logo {
		img {
			height: auto;
			max-height: 40px;
		}
	}
}

// Навигация
.nav {
	display: flex;
	gap: $gap-xxl;
	align-items: center;

	a {
		position: relative;
		transition: color $transition-medium;

		&.router-link-exact-active:not(.btn) {
			color: $primary-green;

			&::after {
				content: "";
				position: absolute;
				left: 0;
				right: 0;
				bottom: -4px;
				height: 2px;
				background: currentColor;
				border-radius: 1px;
			}
		}

		&.accent {
			border: 3px dashed $primary-green;
		}
	}

	// Мобильная навигация - исправленная версия для Safari/Firefox
	@include mobile-menu {
		@include fixed-cover;
		@include flex-column;
		align-items: flex-end;
		background: $overlay-background;
		width: 100vw;
		height: 100vh;
		gap: 28px;
		padding: 90px 22px 22px 22px;
		border-radius: 0 0 18px 18px;
		z-index: $z-index-menu;

		// Используем visibility и opacity вместо transform для лучшей совместимости
		visibility: hidden;
		opacity: 0;
		transition:
			visibility 0s linear 0.25s,
			opacity 0.25s ease-in-out;

		// Альтернативный подход с transform для плавности
		transform: translateX(100%);
		transition:
			transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
			opacity 0.25s ease-in-out,
			visibility 0s linear 0.25s;

		&.open {
			visibility: visible;
			opacity: 1;
			transform: translateX(0);
			transition:
				transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
				opacity 0.25s ease-in-out;
		}
	}
}

// Бургер меню - улучшенная версия
.burger {
	display: none;
	@include flex-column;
	gap: 6px;
	background: none;
	border: none;
	cursor: pointer;
	z-index: $z-index-burger;
	padding: 7px;
	margin-left: 18px;
	transition: transform 0.18s ease;

	// Добавляем touch-action для лучшей работы на мобильных
	touch-action: manipulation;

	@include mobile-menu {
		display: flex;
	}

	span {
		display: block;
		width: 30px;
		height: 4px;
		border-radius: 2px;
		background: $primary-green;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		transform-origin: center;

		// Добавляем will-change для оптимизации
		will-change: transform, opacity;
	}

	&.active {
		span {
			&:nth-child(1) {
				transform: translateY(10px) rotate(45deg);
			}

			&:nth-child(2) {
				opacity: 0;
				transform: scale(0);
			}

			&:nth-child(3) {
				transform: translateY(-10px) rotate(-45deg);
			}
		}
	}
}

// Задний фон меню
.menu-backdrop {
	@include fixed-cover;
	z-index: $z-index-backdrop;
	background: rgba(12, 16, 13, 0.33);
	@include backdrop-blur(1.5px);
	animation: fadeInBackdrop 0.22s;
}

// Анимации
@keyframes slideInMenu {
	from {
		transform: translateX(100%);
		opacity: 0.2;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes fadeInBackdrop {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

// Переходы для backdrop
.fade-menu-enter-active,
.fade-menu-leave-active {
	transition: opacity 0.18s;
}

.fade-menu-enter-from,
.fade-menu-leave-to {
	opacity: 0;
}
