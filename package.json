{"name": "apollon-nuxt", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "^3.5.1", "@nuxt/eslint": "^1.4.0", "@nuxt/fonts": "^0.11.4", "@nuxt/image": "^1.10.0", "@tabler/icons-vue": "^3.33.0", "@unhead/vue": "^2.0.9", "better-sqlite3": "^12.2.0", "eslint": "^9.27.0", "nuxt": "^3.17.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"nuxt-yandex-metrika": "^1.2.9"}}