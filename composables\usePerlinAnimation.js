// composables/usePerlinAnimation.js

import { onMounted, onUnmounted } from "vue";

export function usePerlinAnimation(canvasRef) {
	let worker = null;

	const initWorker = () => {
		if (!canvasRef.value) return;

		// Создаем worker
		// Путь к файлу воркера должен быть из папки public
		worker = new Worker("/workers/perlin.worker.js");

		// Передаем управление холстом в worker с помощью OffscreenCanvas
		const offscreenCanvas = canvasRef.value.transferControlToOffscreen();
		const payload = {
			canvas: offscreenCanvas,
			width: window.innerWidth,
			height: window.innerHeight,
		};

		worker.postMessage({ type: "init", payload }, [offscreenCanvas]);
	};

	const handleResize = () => {
		if (worker) {
			worker.postMessage({
				type: "resize",
				payload: {
					width: window.innerWidth,
					height: window.innerHeight,
				},
			});
		}
	};

	onMounted(() => {
		// Ждем следующего тика, чтобы canvas точно был в DOM
		nextTick(() => {
			initWorker();
			window.addEventListener("resize", handleResize);
		});
	});

	onUnmounted(() => {
		window.removeEventListener("resize", handleResize);
		if (worker) {
			worker.terminate(); // Очень важно "убить" worker при размонтировании
		}
	});
}
