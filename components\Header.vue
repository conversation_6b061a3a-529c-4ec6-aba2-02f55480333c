<!-- eslint-disable vue/multi-word-component-names -->
<template>
	<header class="header">
		<NuxtLink to="/" class="logo">
			<img src="@/assets/Logo.svg" />
		</NuxtLink>
		<nav class="nav" :class="{ open: menuOpen }">
			<NuxtLink to="/" @click="closeMenu">Главная</NuxtLink>
			<NuxtLink to="/services" @click="closeMenu">Услуги</NuxtLink>
			<NuxtLink to="/cases" @click="closeMenu">Кейсы</NuxtLink>
			<NuxtLink to="/contacts" @click="closeMenu">Контакты</NuxtLink>
			<NuxtLink to="/contacts" class="btn accent" @click="closeMenu"
				>Заказать сайт</NuxtLink
			>
		</nav>
		<button
			class="burger"
			:class="{ active: menuOpen }"
			aria-label="Открыть меню"
			@click="toggleMenu"
		>
			<span />
			<span />
			<span />
		</button>
		<transition name="fade-menu">
			<div v-if="menuOpen" class="menu-backdrop" @click="closeMenu" />
		</transition>
	</header>
</template>

<script setup>
import { NuxtLink } from "#components";
import { ref } from "vue";
const menuOpen = ref(false);
function toggleMenu() {
	menuOpen.value = !menuOpen.value;
}
function closeMenu() {
	menuOpen.value = false;
}
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_header.scss */
</style>
