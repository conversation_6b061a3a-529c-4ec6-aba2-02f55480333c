<!-- eslint-disable vue/multi-word-component-names -->
<template>
	<header>
		<NuxtLink to="/">
			<img src="@/assets/Logo.svg" />
		</NuxtLink>
		<nav :class="{ open: menuOpen }">
			<NuxtLink to="/" @click="closeMenu">Главная</NuxtLink>
			<NuxtLink to="/services" @click="closeMenu">Услуги</NuxtLink>
			<NuxtLink to="/cases" @click="closeMenu">Кейсы</NuxtLink>
			<NuxtLink to="/contacts" @click="closeMenu">Контакты</NuxtLink>
			<NuxtLink to="/contacts" class="btn accent" @click="closeMenu"
				>Заказать сайт</NuxtLink
			>
		</nav>
		<button
			class="burger"
			:class="{ active: menuOpen }"
			aria-label="Открыть меню"
			@click="toggleMenu"
		>
			<span />
			<span />
			<span />
		</button>
		<transition name="fade-menu">
			<div v-if="menuOpen" class="menu-backdrop" @click="closeMenu" />
		</transition>
	</header>
</template>

<script setup>
import { NuxtLink } from "#components";
import { ref } from "vue";
const menuOpen = ref(false);
function toggleMenu() {
	menuOpen.value = !menuOpen.value;
}
function closeMenu() {
	menuOpen.value = false;
}
</script>

<style scoped>
header {
	display: flex;
	justify-content: space-between;
	position: fixed;
	filter: drop-shadow(0 0 1rem rgba(0, 0, 0, 0.8));
	width: 100%;
	padding: 24px 50px;
	left: 0;
	top: 0;
	align-items: center;
	z-index: 10;
}

header::before {
	content: "";
	position: absolute;
	inset: 0;
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
	-webkit-mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
	pointer-events: none;
}

nav {
	display: flex;
	gap: 35px;
	align-items: center;
	transition: right 0.22s;
}

nav :deep(a.router-link-exact-active):not(.btn) {
	position: relative;
	color: #569f7a;
}

nav :deep(a.router-link-exact-active):not(.btn)::after {
	content: "";
	position: absolute;
	left: 0;
	right: 0;
	bottom: -4px;
	height: 2px;
	background: currentColor;
	border-radius: 1px;
}

.accent {
	border: 3px dashed #569f7a;
}

header:has(nav.open) {
	background: rgba(20, 28, 23, 0.97);
}

.burger {
	display: none;
	flex-direction: column;
	gap: 6px;
	background: none;
	border: none;
	cursor: pointer;
	z-index: 21;
	padding: 7px;
	margin-left: 18px;
	transition: transform 0.18s;
}
.burger span {
	display: block;
	width: 30px;
	height: 4px;
	border-radius: 2px;
	background: #569f7a;
	transition: all 0.2s;
}
.burger.active span:nth-child(1) {
	transform: translateY(10px) rotate(45deg);
}
.burger.active span:nth-child(2) {
	opacity: 0;
}
.burger.active span:nth-child(3) {
	transform: translateY(-10px) rotate(-45deg);
}

/* Мобильное меню */
@media (max-width: 900px) {
	header {
		padding: 24px 32px;
	}
	nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		flex-direction: column;
		align-items: flex-end;
		background: rgba(20, 28, 23, 0.97);
		width: 100vw;
		height: 100vh;
		gap: 28px;
		padding: 90px 22px 22px 22px;
		border-radius: 0 0 18px 18px;
		transition:
			transform 0.25s cubic-bezier(0.4, 1.3, 0.5, 1),
			opacity 0.17s;
		transform: translateX(100%);
		opacity: 0;
		pointer-events: none;
		z-index: 20;
	}
	nav.open {
		transform: translateX(0);
		opacity: 1;
		pointer-events: auto;
		animation: slideInMenu 0.22s cubic-bezier(0.3, 1.2, 0.2, 1);
	}
	@keyframes slideInMenu {
		from {
			transform: translateX(100%);
			opacity: 0.2;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}
	.burger {
		display: flex;
	}
}

/* Задний полупрозрачный фон */
.menu-backdrop {
	position: fixed;
	inset: 0;
	z-index: 9;
	background: rgba(12, 16, 13, 0.33);
	backdrop-filter: blur(1.5px);
	animation: fadeInBackdrop 0.22s;
}
@keyframes fadeInBackdrop {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}
.fade-menu-enter-active,
.fade-menu-leave-active {
	transition: opacity 0.18s;
}
.fade-menu-enter-from,
.fade-menu-leave-to {
	opacity: 0;
}
</style>
