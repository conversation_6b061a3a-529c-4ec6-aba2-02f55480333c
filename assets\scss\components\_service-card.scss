@use "../variables" as *;
@use "../mixins" as *;

// Service Card стили
.service-card {
	@include card-base;
	padding: 20px;
	color: $text-primary;
	@include flex-column;
	position: relative;
	min-width: 0;

	.card-content {
		@include flex-column;
		height: 100%;
		width: 100%;
		justify-content: space-between;

		&.cols-2 {
			flex-direction: row;

			.icon.right {
				margin-left: 18px;
				order: 2;
				margin: auto;
			}

			.text {
				order: 1;
			}

			@include mobile {
				flex-direction: column-reverse;

				.icon.right {
					width: 75px;
					height: 75px;
					margin: 0;
				}
			}
		}

		&.cols-1 .icon.left {
			margin-right: 18px;
			order: 0;
		}

		.text {
			min-width: 0;
		}
	}

	&:has(.card-content.cols-1) {
		aspect-ratio: 1/1;

		@include mobile {
			aspect-ratio: initial;
		}
	}

	.title {
		font-size: $font-size-xl;
		margin-top: 10px;
	}

	.desc {
		font-size: $font-size-medium;
	}

	.icon {
		flex: none;
		@include flex-center;
		aspect-ratio: 1;
	}
}

// Service Grid
.service-grid {
	@include grid-base;

	@include mobile {
		grid-template-columns: 1fr;
	}

	@include tablet {
		.service-card {
			grid-column: span 1 !important;
		}
	}
}
