// Импорт переменных и миксинов
@import "variables";
@import "mixins";

// Базовые стили
* {
	box-sizing: border-box;
}

html {
	scrollbar-color: $primary-green transparent;
}

::selection {
	background-color: $primary-green;
}

body {
	margin: 0;
	box-shadow: $shadow-inset;
	background: $gradient-background;
	color: $text-primary;
	font-family: $font-family-primary;
	font-size: $font-size-base;
	letter-spacing: $letter-spacing;
	min-height: 100dvh;
	@include flex-column-center;
	padding: $body-padding;

	@include mobile {
		padding: $body-padding-mobile;
	}
}

section {
	width: 100%;

	> h1 {
		@include text-clamp(2em, 5vw, 4em);
		margin: 0;
		margin-top: 0.75em;
		font-weight: $font-weight-medium;

		@include mobile {
			margin-top: 1.5em;
		}
	}
}

// Ссылки
a {
	color: currentColor;
	text-decoration: none;
	transition: $transition-slow;
}

// Кнопки
.btn {
	@include button-primary;

	@include mobile {
		width: 100%;
		justify-content: center;
	}

	&.accent {
		@include button-accent;
	}
}

// SVG
svg {
	vertical-align: middle;
}

// Контент секции
section,
.content {
	@include flex-column;
	gap: $gap-large;
}

// Формы
input,
button,
textarea {
	font-family: $font-family-primary;
	letter-spacing: $letter-spacing;
}

// Переходы страниц
.page-enter-active,
.page-leave-active {
	@include page-transition;
}

.page-enter-from {
	opacity: 0;
	transform: translateX(30px);
}

.page-leave-to {
	opacity: 0;
	transform: translateX(-30px);
}

// Анимация загрузки макета
.layout-enter-active {
	transition: all 0.8s ease;

	&-from {
		opacity: 0;
	}
}

// Импорт компонентов
@import "components/header";
@import "components/hero";
@import "components/case-card";
@import "components/service-card";
@import "components/contacts";
@import "components/perlin-noise";
