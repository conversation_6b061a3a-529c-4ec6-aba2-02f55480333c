@use "../variables" as *;
@use "../mixins" as *;

// Contacts стили
.contacts-section {
	display: flex;
	gap: $gap-xl;

	@include tablet {
		flex-direction: column;
		padding: 18px 6px;
	}
}

.contacts-left,
.contacts-right {
	flex: 1 1 340px;
	@include flex-column;
	gap: 24px;
}

.contact-card {
	background: $card-background;
	border-radius: $border-radius-large;
	padding: 28px 24px;
	color: $text-primary;
	width: 100%;
	font-size: 1.1rem;
	@include flex-column;
	justify-content: space-between;
	@include backdrop-blur(9px);

	&.vk {
		background: radial-gradient(
			152.53% 141.42% at 0% 100%,
			#0077ff33 0%,
			#52525233 100%
		);
	}

	&.tg {
		background: radial-gradient(
			152.53% 141.42% at 100% 100%,
			#26a5e433 0%,
			#52525233 100%
		);
	}

	&.vk,
	&.tg {
		aspect-ratio: 1;
		gap: 20px;

		@include tablet {
			aspect-ratio: initial !important;
		}
	}

	&.social {
		@include flex-column;
		gap: 16px;
	}
}

.socials {
	display: flex;
	gap: 24px;

	@include tablet {
		flex-wrap: wrap;
	}
}

.contact-title {
	font-weight: $font-weight-bold;
	font-size: 2em;
}

.contact-sub {
	color: rgba(255, 255, 255, 0.6);
	font-size: 1.2em;
}

.contact-label {
	color: $text-muted;
	margin-bottom: 2px;
	@include text-clamp(1em, 1.5vw, 1.3em);

	&.phone {
		margin-top: 18px;
	}
}

.contact-email,
.contact-phone {
	color: $text-primary;
	@include text-clamp(1em, 2.5vw, 2em);
}

// Форма контактов
.contacts-form {
	background: $card-background;
	border-radius: $border-radius-large;
	@include backdrop-blur;
	will-change: backdrop-filter;
	padding: 28px 24px;
	@include flex-column;
	gap: 18px;
	color: $text-primary;
	height: 100%;
	justify-content: center;

	.form-title {
		font-size: 2em;
		font-weight: $font-weight-bold;
		margin-bottom: 8px;
	}

	input,
	textarea {
		background: #101010;
		border: none;
		border-radius: $border-radius-small;
		color: $text-primary;
		padding: 13px 14px;
		font-size: 1em;
		outline: none;
		margin-bottom: 0;
	}

	textarea {
		min-height: 68px;
		resize: vertical;
	}

	.form-policy {
		font-size: $font-size-small;
		color: #b6b6b6;
		line-height: 1.4;
	}

	.contacts-submit {
		background: #1cb17c;
		color: $text-primary;
		font-weight: $font-weight-semibold;
		border: none;
		border-radius: $gap-small;
		padding: 13px;
		cursor: pointer;
		font-size: $font-size-large;
		margin-top: 10px;
		@include flex-center;
		gap: 10px;
		transition: background $transition-medium;

		&:hover {
			background: #179a6a;
		}
	}

	.btn-icon {
		width: 22px;
		height: 22px;
		fill: $text-primary;
	}

	.form-success {
		color: $text-success;
		margin-top: 9px;
		font-size: 1.08em;
	}
}
