// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from "nuxt/config"; // ЭТО ПРАВИЛЬНЫЙ ИМПОРТ

export default defineNuxtConfig({
	compatibilityDate: "2025-05-15",

	devtools: { enabled: false },
	experimental: {
		payloadExtraction: true,
	},

	vite: {
		ssr: {
			noExternal: ["@nuxt/content", "@nuxt/image"],
		},
	},

	modules: [
		"@nuxt/content",
		"@nuxt/fonts",
		"@nuxt/image",
		"@nuxt/eslint",
		"nuxt-yandex-metrika",
	],

	css: ["@/assets/main.css", "@/assets/adaptive.css"],

	fonts: {
		families: [
			{
				name: "SpaceGrotesk",
				src: "/fonts/SpaceGrotesk-Medium.woff2",
				weight: "500",
				style: "normal",
			},
			{
				name: "SpaceGrotesk",
				src: "/fonts/SpaceGrotesk-Regular.woff2",
				weight: "400",
				style: "normal",
			},
			{
				name: "SpaceGrotesk",
				src: "/fonts/SpaceGrotesk-Light.woff2",
				weight: "300",
				style: "normal",
			},
			{
				name: "Caveat",
				src: "/fonts/Caveat-Regular.ttf",
				weight: "400",
				style: "normal",
			},
		],
		display: "swap", // для оптимизации загрузки шрифта
	},

	image: {
		// Лучше явно указать provider
		provider: "static",
		dir: "public",
		// domains: ['...'], если нужно для внешних источников
		screens: {
			sm: 320,
			md: 640,
			lg: 1024,
			xl: 1280,
			"2xl": 1536,
		},
	},

	app: {
		head: {
			// 1. Свойство 'title' ДОЛЖНО быть строкой.
			// Это базовый (по умолчанию) заголовок для всего сайта,
			// если на странице не задан свой заголовок и titleTemplate не используется
			// или не формирует его.
			title: "Apollon Digital", // <-- ЭТО ДОЛЖНА БЫТЬ СТРОКА!
			htmlAttrs: {
				lang: "ru",
			},
			meta: [
				{ charset: "utf-8" },
				{ name: "viewport", content: "width=device-width, initial-scale=1" },
				{ name: "format-detection", content: "telephone=no" },

				// SEO
				{
					name: "description",
					content: "Apollon Digital — цифровые кейсы и проекты.",
				},
				{ name: "author", content: "Apollon Digital Team" },
				{ name: "theme-color", content: "#0d0d0d" },

				// Open Graph / Social
				{ property: "og:title", content: "Apollon Digital" },
				{
					property: "og:description",
					content: "Современные цифровые кейсы от Apollon Digital.",
				},
				{ property: "og:type", content: "website" },
				{ property: "og:image", content: "/new/preview.jpg" },
				{ property: "og:url", content: "https://yourdomain.com/new/" },
			],
			link: [
				{
					rel: "icon",
					type: "image/vnd.microsoft.icon",
					href: "/new/favicon.ico",
				},
				// Предзагрузка шрифтов (ускорение)
				{
					rel: "preload",
					as: "font",
					href: "/fonts/SpaceGrotesk-Medium.woff2",
					type: "font/woff2",
					crossorigin: "anonymous",
				},
				{
					rel: "preload",
					as: "font",
					href: "/fonts/SpaceGrotesk-Regular.woff2",
					type: "font/woff2",
					crossorigin: "anonymous",
				},
				{
					rel: "preload",
					as: "font",
					href: "/fonts/SpaceGrotesk-Light.woff2",
					type: "font/woff2",
					crossorigin: "anonymous",
				},
			],
		},

		// 🗂 Папка, в которую заливаешь (например, /new/)
		baseURL: "/new/",
	},
	yandexMetrika: {
		id: "99021332",
		// debug: process.env.NODE_ENV !== "production",
		// delay: 0,
		// cdn: false,
		// verification: null, // Verification in Yandex Webmaster
		options: {
			clickmap: true,
			trackLinks: true,
			accurateTrackBounce: true,
			webvisor: true,
		},
	},
});
