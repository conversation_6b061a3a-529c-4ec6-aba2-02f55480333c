<template>
	<div class="case-grid">
		<CaseCard
			v-for="c in cases"
			:key="c.id"
			:title="c.title"
			:desc="c.desc"
			:tags="c.tags"
			:bg="c.bg"
			:gradient="c.gradient"
			:link="c.link"
			:cols="c.cols"
			:class="[c.class, c.cols === 2 ? 'case-wide' : '']"
		/>
	</div>
</template>

<script setup>
import CaseCard from "./CaseCard.vue";

// Пример массива тегов
const tagList = [
	{
		id: 1,
		icon: "IconGraph",
		label: "Маркетинг",
		color: "#fff",
		bg: "#7b2222",
	},
	{
		id: 2,
		icon: "IconShoppingCart",
		label: "Интернет-магазин",
		color: "#fff",
		bg: "#b49337",
	},
	{
		id: 3,
		icon: "IconHeadset",
		label: "Поддержка",
		color: "#fff",
		bg: "#252d5c",
	},
];

// Пример кейсов
const cases = [
	{
		id: 1,
		title: "ВЛАДИС.ОРСК",
		desc: "развитие социальных сетей агентства недвижимости",
		tags: [tagList[0]],
		bg: "cases-bg/vladis.webp",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/vladis",
		cols: 1,
		class: "",
	},
	{
		id: 2,
		title: "Дубликаты 56",
		desc: "сайт для изготовлении дубликата автомобильного номера",
		tags: [tagList[1], tagList[2]],
		bg: "cases-bg/dub56.png",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/dub56",
		cols: 2,
		class: "case-wide",
	},
	{
		id: 3,
		title: "ВЛАДИС.ОРСК",
		desc: "развитие социальных сетей агентства недвижимости",
		tags: [tagList[0]],
		bg: "cases-bg/vladis.webp",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/vladis",
		cols: 1,
		class: "",
	},
	{
		id: 4,
		title: "Дубликаты 56",
		desc: "сайт для изготовлении дубликата автомобильного номера",
		tags: [tagList[1], tagList[2]],
		bg: "cases-bg/dub56.png",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/dub56",
		cols: 2,
		class: "case-wide",
	},
	{
		id: 5,
		title: "ВЛАДИС.ОРСК",
		desc: "развитие социальных сетей агентства недвижимости",
		tags: [tagList[0]],
		bg: "cases-bg/vladis.webp",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/vladis",
		cols: 1,
		class: "",
	},
	{
		id: 6,
		title: "ВЛАДИС.ОРСК",
		desc: "развитие социальных сетей агентства недвижимости",
		tags: [tagList[0]],
		bg: "cases-bg/vladis.webp",
		gradient: "rgba(34,48,38)",
		link: "https://example.com/vladis",
		cols: 1,
		class: "",
	},
];
</script>

<style scoped>
/* Стили теперь в assets/scss/components/_case-card.scss */
</style>
