@use "../variables" as *;
@use "../mixins" as *;

// Case Card стили
.case-card {
	@include case-card;

	.overlay {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 100%;
		min-height: 100%;
		padding: 14px;
	}

	.main {
		flex: 1 1 auto;
		min-width: 0;
	}

	.title {
		font-size: $font-size-xl;
		text-shadow: $shadow-text;
		line-height: 1.1;
	}

	.desc {
		font-size: $font-size-medium;
		margin-bottom: 0.3em;
		line-height: 1.25;
		font-weight: $font-weight-light;
	}

	.tags {
		display: flex;
		gap: $gap-small;
		flex-wrap: wrap;
	}

	.next-btn {
		margin-left: 18px;
		flex-shrink: 0;
		margin-top: 14px;
		width: 54px;
		height: 54px;
		border-radius: 50%;
		background: $gradient-primary;
		@include flex-center;
		transition: background $transition-medium;

		.icon {
			color: $text-primary;
		}
	}

	&:hover .next-btn {
		background: $gradient-primary-hover;
	}
}

// Case Grid
.case-grid {
	@include case-grid;
}

// Case Tag
.case-tag {
	display: inline-flex;
	align-items: center;
	gap: 7px;
	border-radius: $border-radius-medium;
	font-size: 1.02rem;
	font-weight: $font-weight-medium;
	padding: 0.28em 1.15em 0.28em 0.7em;
	background: rgba(255, 255, 255, 0.08);
	color: $text-primary;
	user-select: none;
	transition:
		background $transition-medium,
		color $transition-medium;
	line-height: 1.2;

	.tag-icon {
		flex-shrink: 0;
	}
}
