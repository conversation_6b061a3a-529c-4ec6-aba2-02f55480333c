* {
    box-sizing: border-box;
}

html {
    scrollbar-color: #569f7a transparent;
}

::selection {
    background-color: #569f7a;
}

body {
    margin: 0;
    box-shadow: inset 0 0 100px 0 rgba(0, 0, 0, 0.5);
    background: radial-gradient(circle at 0% 90%, #00824e33 0%, transparent 50%), radial-gradient(circle at 0% 100%, transparent 50%,  #375C4A33 100%), #0D0D0D;
    color: #fff;
    font-family: 'SpaceGrotesk', system-ui, -apple-system, sans-serif;
    font-size: 1.15em;
    letter-spacing: -0.08em;
    min-height: 100dvh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 35px 50px;
}

section {
    width: 100%;
}

a {
    color: currentColor;
    text-decoration: none;
}

a {
    transition: .5s;
}

a.btn {
    padding: 16px 24px;
    border-radius: 50px;
    background-color: rgba(82, 82, 82, 0.2);
    color: #bbbbbb;
    border: solid 2px #bbbbbb;
    box-shadow: 0 5px 12px 0 rgba(86, 159, 122, 0.15);
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 10px;
    overflow: hidden;

    &:hover {
        filter: brightness(1.25);
    }
}

svg {
    vertical-align: middle;
}

.btn.accent {
    background-color: rgba(0, 163, 97, 0.2);
    color: #569f7a;
    box-shadow: 0 5px 12px 0 rgba(86, 159, 122, 0.15);
    border: 0;
}

.btn.accent::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: skewX(-20deg);
    transition: all 0.7s ease;
    opacity: 0;
}

.btn.accent:hover::before {
    left: 125%;
    opacity: 1;
}

section>h1 {
    font-size: 4em;
    font-size: clamp(2em, 5vw, 4em);
    margin: 0;
    margin-top: .75em;
    font-weight: 500;
}

section,
.content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

input,
button,
textarea {
    font-family: 'SpaceGrotesk', system-ui, -apple-system, sans-serif;
    letter-spacing: -0.08em;
}