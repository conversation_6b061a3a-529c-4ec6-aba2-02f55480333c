// Hero компонент
.hero {
  section,
  .content {
    align-items: center;
    text-align: center;
    justify-content: center;
  }

  .btns {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .title {
    @include text-clamp(2.5rem, 4vw, 3.5em);
    line-height: 90%;
    max-width: 1100px;
    @include text-balance;
  }

  .serif {
    font-family: $font-family-accent;
    color: $primary-green;
    @include text-clamp(4rem, 4vw, 5rem);
  }

  .status {
    padding: 12px;
    border-radius: $border-radius-round;
    background-color: rgba(255, 255, 255, 0.2);

    &:before {
      content: "• ";
      font-size: 2em;
      line-height: 0;
      color: $primary-green;
      vertical-align: middle;
    }

    &.green {
      background-color: $primary-green-alpha;
    }
  }

  // Анимация появления
  .fade-slide-enter-active {
    @include fade-slide-enter;
  }

  .fade-slide-enter-from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(5px);
  }
}
