// Цвета
$primary-green: #569f7a;
$primary-green-light: #00a361;
$primary-green-dark: #375c4a;
$primary-green-alpha: rgba(0, 163, 97, 0.2);

$background-dark: #0d0d0d;
$background-gradient-1: #00824e33;
$background-gradient-2: #375c4a33;

$text-primary: #fff;
$text-secondary: #bbbbbb;
$text-muted: #8e9296;
$text-success: #38e586;

$card-background: rgba(82, 82, 82, 0.2);
$card-background-dark: #222;
$overlay-background: rgba(20, 28, 23, 0.97);

// Размеры и отступы
$header-padding: 24px 50px;
$header-padding-mobile: 24px 32px;
$body-padding: 35px 50px;
$body-padding-mobile: 30px;

$border-radius-small: 8px;
$border-radius-medium: 16px;
$border-radius-large: 20px;
$border-radius-xl: 32px;
$border-radius-round: 50px;

$gap-small: 12px;
$gap-medium: 20px;
$gap-large: 25px;
$gap-xl: 32px;
$gap-xxl: 35px;

// Шрифты
$font-family-primary: 'SpaceGrotesk', system-ui, -apple-system, sans-serif;
$font-family-accent: 'Caveat', serif;

$font-size-base: 1.15em;
$font-size-small: 0.94em;
$font-size-medium: 1.05rem;
$font-size-large: 1.12em;
$font-size-xl: 2rem;
$font-size-xxl: 3.5em;
$font-size-xxxl: 4em;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$letter-spacing: -0.08em;

// Тени
$shadow-small: 0 2px 24px 0 rgba(0, 0, 0, 0.1);
$shadow-medium: 0 5px 12px 0 rgba(86, 159, 122, 0.15);
$shadow-large: 0 8px 32px 0 rgba(0, 0, 0, 0.16);
$shadow-inset: inset 0 0 100px 0 rgba(0, 0, 0, 0.5);
$shadow-text: 0 2px 8px rgba(0, 0, 0, 0.14);
$shadow-header: drop-shadow(0 0 1rem rgba(0, 0, 0, 0.8));

// Переходы и анимации
$transition-fast: 0.13s;
$transition-medium: 0.2s;
$transition-slow: 0.5s;
$transition-extra-slow: 0.7s;

$transition-easing-default: ease;
$transition-easing-smooth: cubic-bezier(0.4, 1.3, 0.5, 1);
$transition-easing-bounce: cubic-bezier(0.3, 1.2, 0.2, 1);
$transition-easing-page: cubic-bezier(0.075, 0.82, 0.165, 1);

// Брейкпоинты
$breakpoint-sm: 320px;
$breakpoint-md: 640px;
$breakpoint-lg: 768px;
$breakpoint-xl: 900px;
$breakpoint-xxl: 1200px;

// Z-индексы
$z-index-background: -1;
$z-index-backdrop: 9;
$z-index-header: 10;
$z-index-menu: 20;
$z-index-burger: 21;

// Градиенты
$gradient-primary: linear-gradient(135deg, #2ecc71 70%, #44cabe 100%);
$gradient-primary-hover: linear-gradient(135deg, #44cabe 60%, #2ecc71 100%);
$gradient-background: radial-gradient(circle at 0% 90%, #{$background-gradient-1} 0%, transparent 50%), 
                     radial-gradient(circle at 0% 100%, transparent 50%, #{$background-gradient-2} 100%), 
                     #{$background-dark};

// Размеры сетки
$grid-gap: 20px;
$grid-gap-large: 32px;
$grid-gap-mobile: 12px;
$grid-min-width: 300px;
$grid-min-width-case: 340px;
$grid-columns-max: 4;
$grid-columns-medium: 3;
$grid-columns-mobile: 1;
